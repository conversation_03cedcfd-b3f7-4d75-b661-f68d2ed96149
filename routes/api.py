from flask import request, jsonify, session, flash, redirect, url_for, current_app # Added current_app
from datetime import datetime, timedelta
import google.generativeai as genai
from pinecone import Pinecone
import os
import re
import json
import base64
import urllib.parse
import time
from models import db, Part, Submission, IncompleteSubmission, ProblemSetSubmission, MarkingPoint, DailyActivity, DailyActiveTime, User, Question
# Import utilities and decorators
from .utils import login_required, update_user_activity, error_logger, app_logger

# Note: AI clients (Groq, Mistral) might be better initialized in the app factory
# and passed here, or accessed via app.config/current_app.
# For now, assume they are passed to the registration function.

def register_api_routes(app, db, session, limiter, groq_client, mistral_client): # Pass limiter and AI clients
    # Configure Google's Gemini API
    genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

    # --- START: Helper function for grading ---
    def _calculate_score_and_evaluated_points(user_answer: str, part_data: Part, gemini_model, app_logger, pinecone_index=None):
        """
        Calculates the score for a given part and returns evaluated marking points.
        This logic is shared between get_git_diff and submit_problemset.
        """
        grading_start_time = time.time()
        timing_info = {}
        total_score = 0
        evaluated_points = []

        if part_data.input_type == 'mcq':
            mcq_start_time = time.time()
            # For MCQ, the answer is the selected option index (passed as user_answer)
            if not user_answer: # Should be validated before calling this helper
                return {'score': 0, 'evaluated_points': [], 'error': 'No answer provided for MCQ'}

            try:
                selected_option_index = int(user_answer)
                options = part_data.options

                if not options or selected_option_index >= len(options) or selected_option_index < 0:
                    return {'score': 0, 'evaluated_points': [], 'error': 'Invalid option selected for MCQ'}

                selected_option = options[selected_option_index]
                is_correct = selected_option.is_correct # Assuming is_correct is a boolean field
                total_score = part_data.score if is_correct else 0

                # For MCQs, create structured feedback based on correctness
                if is_correct:
                    feedback_text = f"Correctly identified {selected_option.description}"
                else:
                    # Find the correct option for feedback
                    correct_option = next((opt for opt in options if opt.is_correct), None)
                    if correct_option:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}' instead of '{correct_option.description}'"
                    else:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}'"

                mcq_end_time = time.time()
                timing_info['mcq_processing'] = round((mcq_end_time - mcq_start_time) * 1000, 2)  # Convert to milliseconds
                timing_info['total_grading_time'] = round((mcq_end_time - grading_start_time) * 1000, 2)

                evaluated_points.append({
                    'id': f"mcq_{part_data.id}",
                    'description': f"Selected option: {selected_option.description}",
                    'score': part_data.score,
                    'achieved': is_correct,
                    'partial': False,
                    'achieved_score': total_score,
                    'evidence': str(selected_option_index),
                    'feedback': feedback_text,
                    'color': 'border-green-400' if is_correct else 'border-red-400'
                })
                return {'score': total_score, 'evaluated_points': evaluated_points, 'timing_info': timing_info}

            except (ValueError, TypeError) as e:
                app_logger.exception(f"Error processing MCQ answer in helper: {str(e)}")
                return {'score': 0, 'evaluated_points': [], 'error': 'Invalid MCQ answer format'}

        # For non-MCQ questions (free response with marking points)
        try:
            free_response_start_time = time.time()
            marking_points_data = [{
                'id': mp.id,
                'description': mp.description,
                'score': mp.score
            } for mp in part_data.marking_points]

            highlight_border_classes = [
                'border-yellow-400', 'border-blue-400', 'border-green-400',
                'border-pink-400', 'border-purple-400', 'border-indigo-400',
                'border-teal-400', 'border-orange-400', 'border-lime-400',
                'border-cyan-400'
            ]
            color_index = 0

            # Initialize timing for individual marking point processing
            timing_info['marking_points'] = []
            timing_info['pinecone_queries'] = []
            timing_info['llm_evaluations'] = []

            for mp_data in marking_points_data:
                mp_start_time = time.time()
                assigned_border_class = highlight_border_classes[color_index % len(highlight_border_classes)]
                color_index += 1

                # Query Pinecone for relevant context
                pinecone_start_time = time.time()
                context_text = ""
                if pinecone_index:
                    try:
                        # Create query text from marking point and question context
                        query_text = f"{mp_data['description']} {part_data.description}"
                        if part_data.question.topic:
                            query_text += f" {part_data.question.topic.name}"
                        if part_data.question.topic.subject:
                            query_text += f" {part_data.question.topic.subject.name}"

                        # Query Pinecone index for relevant context
                        try:
                            query_payload = {
                                "inputs": {
                                    "text": query_text
                                },
                                "top_k": 3
                            }

                            query_response = pinecone_index.search(
                                namespace="__default__",
                                query=query_payload
                            )

                            for item in query_response['result']['hits']:
                                context_text += f"{item['fields']['title']}: {item['fields']['content']}\n"

                        except Exception as embedding_error:
                            app_logger.debug(f"Pinecone query failed: {embedding_error}")
                            query_response = None

                    except Exception as e:
                        app_logger.warning(f"Error querying Pinecone for marking point {mp_data['id']}: {str(e)}")
                        context_text = ""

                pinecone_end_time = time.time()
                pinecone_duration = round((pinecone_end_time - pinecone_start_time) * 1000, 2)
                timing_info['pinecone_queries'].append({
                    'marking_point_id': mp_data['id'],
                    'duration_ms': pinecone_duration,
                    'context_retrieved': len(context_text) > 0
                })

                prompt = f"""\
                    You are an expert examiner evaluating a student's answer against a specific marking point.

                    TASK:
                    1. Determine if the student's answer demonstrates understanding of the marking point
                    2. Classify the answer and provide structured feedback in one of these formats:
                       - If FULLY CORRECT: "Correctly identified <concept>"
                       - If the student attempted to address the concept but was INCORRECT: "Incorrecty explained <concept"
                       - If the student OMITTED the concept entirely: "Omitted <concept>"
                    3. For FULLY or PARTIALLY correct answers, identify the exact text from the student's answer that provides evidence
                    4. Use the context for the marking point to inform your evaluation

                    RESPONSE FORMAT:
                    You must respond in one of these four formats ONLY:

                    Format 1 - If the marking point is FULLY addressed:
                    YES
                    FEEDBACK: Correctly identified <specific concept from marking point>
                    EVIDENCE: <exact text from student's answer>

                    Format 2 - If the marking point was PARTIALLY addressed
                    PARTIAL
                    FEEDBACK: Incorrectly explained <specific misconception or incomplete understanding>
                    EVIDENCE: <exact text from student's answer>

                    Format 3 - If the marking point is NOT addressed
                    NO
                    FEEDBACK: Omitted <specific required concept from marking point>

                    Format 4 - If the marking point is WRONGLY addressed
                    NO
                    FEEDBACK: Incorrectly explained <specific misconception or irrelevant concept>
                    EVIDENCE: <exact text from student's answer>

                    IMPORTANT: Do not include any other text, explanations, or formatting in your response.

                    CONTEXT:
                    {context_text}

                    MARKING POINT: {mp_data['description']}
                    OTHER MARKING POINTS (exclude these from your evaluation): {', '.join([other_mp['description'] for other_mp in marking_points_data if other_mp['id'] != mp_data['id']])}
                    STUDENT'S ANSWER: {user_answer}
                    """

                point_score = 0
                is_correct_mp = False
                is_partial_mp = False
                evidence_mp = None
                feedback_mp = None
                error_mp = False

                try:
                    llm_start_time = time.time()
                    generation_config = {
                        "temperature": 0.3, "top_p": 0.95, "top_k": 40, "max_output_tokens": 4096,
                    }
                    safety_settings = [
                        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                    ]
                    response_text = gemini_model.generate_content(
                        prompt,
                        generation_config=generation_config,
                        safety_settings=safety_settings
                    ).text.strip()

                    llm_end_time = time.time()
                    llm_duration = round((llm_end_time - llm_start_time) * 1000, 2)

                    response_upper = response_text.upper()
                    is_correct_mp = 'YES' in response_upper and not 'PARTIAL' in response_upper
                    is_partial_mp = 'PARTIAL' in response_upper

                    app_logger.debug(f"LLM Response for marking point {mp_data['id']}: {response_text}")

                    # Extract feedback
                    if 'FEEDBACK:' in response_text:
                        feedback_parts = response_text.split('FEEDBACK:', 1)
                        if len(feedback_parts) > 1:
                            feedback_mp = feedback_parts[1].split('EVIDENCE:')[0].strip()
                    elif 'FEEDBACK' in response_text: # Fallback if colon is missing
                        feedback_parts = response_text.split('FEEDBACK', 1)
                        if len(feedback_parts) > 1:
                            feedback_mp = feedback_parts[1].lstrip(':').split('EVIDENCE:')[0].strip()

                    # Extract evidence for correct/partial answers
                    if is_correct_mp or is_partial_mp:
                        if 'EVIDENCE:' in response_text:
                            evidence_parts = response_text.split('EVIDENCE:', 1)
                            if len(evidence_parts) > 1: evidence_mp = evidence_parts[1].strip()
                        elif 'EVIDENCE' in response_text: # Fallback if colon is missing
                            evidence_parts = response_text.split('EVIDENCE', 1)
                            if len(evidence_parts) > 1: evidence_mp = evidence_parts[1].lstrip(':').strip()

                    if is_correct_mp:
                        point_score = mp_data['score']
                    elif is_partial_mp:
                        point_score = mp_data['score'] * 0.5

                except Exception as e_mp:
                    app_logger.exception(f"Error evaluating marking point {mp_data['id']} with LLM: {str(e_mp)}")
                    error_mp = True
                    llm_duration = 0  # Set duration to 0 if there was an error

                # Record LLM evaluation timing
                timing_info['llm_evaluations'].append({
                    'marking_point_id': mp_data['id'],
                    'duration_ms': llm_duration,
                    'result': 'correct' if is_correct_mp else ('partial' if is_partial_mp else 'incorrect'),
                    'error': error_mp
                })

                total_score += point_score

                # Record individual marking point timing
                mp_end_time = time.time()
                mp_duration = round((mp_end_time - mp_start_time) * 1000, 2)
                timing_info['marking_points'].append({
                    'marking_point_id': mp_data['id'],
                    'total_duration_ms': mp_duration,
                    'achieved_score': point_score,
                    'max_score': mp_data['score']
                })

                evaluated_points.append({
                    'id': mp_data['id'],
                    'description': mp_data['description'],
                    'score': mp_data['score'],
                    'achieved': is_correct_mp,
                    'partial': is_partial_mp,
                    'achieved_score': point_score,
                    'evidence': evidence_mp,
                    'feedback': feedback_mp,
                    'color': assigned_border_class if (is_correct_mp or is_partial_mp) and evidence_mp else None,
                    'error': error_mp
                })

            # Calculate total timing for free-response processing
            free_response_end_time = time.time()
            timing_info['free_response_processing'] = round((free_response_end_time - free_response_start_time) * 1000, 2)
            timing_info['total_grading_time'] = round((free_response_end_time - grading_start_time) * 1000, 2)

            # Add summary statistics
            timing_info['summary'] = {
                'total_marking_points': len(marking_points_data),
                'total_pinecone_time_ms': sum(pq['duration_ms'] for pq in timing_info['pinecone_queries']),
                'total_llm_time_ms': sum(le['duration_ms'] for le in timing_info['llm_evaluations']),
                'average_mp_time_ms': round(sum(mp['total_duration_ms'] for mp in timing_info['marking_points']) / len(timing_info['marking_points']), 2) if timing_info['marking_points'] else 0
            }

            return {'score': total_score, 'evaluated_points': evaluated_points, 'timing_info': timing_info}

        except Exception as e:
            app_logger.exception(f"Error processing free-response answer in helper: {str(e)}")
            # Return 0 score and empty points if a major error occurs in this block
            return {'score': 0, 'evaluated_points': [], 'error': 'Error processing marking points'}
    # --- END: Helper function for grading ---

    # --- START: New Supabase Auth Confirmation Route ---
    @app.route('/api/auth/confirm', methods=['GET'])
    def confirm_auth():
        """
        Handles email confirmation links (e.g., password reset, email change) using Supabase PKCE flow.
        Verifies the token_hash and type, or exchanges code for session, and redirects.
        """
        # Check for code parameter (new PKCE flow)
        code = request.args.get('code')
        if code:
            app_logger.info(f"Auth confirmation request received with code parameter")
            next_url = request.args.get('next', url_for('login'))  # Default redirect to login

            try:
                # Access the Supabase client
                from app import supabase

                if not supabase:
                    error_logger.critical("Supabase client not available in confirm_auth route.")
                    flash('Authentication system configuration error.', 'error')
                    return redirect(url_for('login'))

                # Exchange the code for a session
                app_logger.info(f"Attempting to exchange code for session...")
                exchange_response = supabase.auth.exchange_code_for_session({
                    "auth_code": code
                })
                app_logger.info(f"Code exchange response: {exchange_response}")

                # Check if exchange was successful
                if exchange_response and exchange_response.user:
                    supabase_email = exchange_response.user.email
                    app_logger.info(f"Successfully exchanged code for session. User email: {supabase_email}")

                    # Store the email in session
                    session['supabase_email'] = supabase_email
                    session['auth_timestamp'] = datetime.now().timestamp()
                    session.permanent = True
                    session.modified = True

                    # Validate the next_url to prevent open redirect vulnerabilities
                    if next_url and next_url.startswith('/'):
                        # Flash success message
                        flash('Verification successful! Please set your new password.', 'success')
                        return redirect(next_url)
                    else:
                        # Invalid 'next' URL provided
                        error_logger.warning(f"Invalid 'next' URL provided: {next_url}. Redirecting to login.")
                        flash('Verification successful! Please log in.', 'success')
                        return redirect(url_for('login'))
                else:
                    error_logger.error(f"Code exchange failed. Response: {exchange_response}")
                    flash('Verification failed. The link may be invalid or expired.', 'error')
                    return redirect(url_for('login'))

            except Exception as e:
                error_logger.exception(f"Error during code exchange: {str(e)}")
                flash('An unexpected error occurred during verification.', 'error')
                return redirect(url_for('login'))

        # Legacy flow with token_hash
        token_hash = request.args.get('token_hash')
        auth_type = request.args.get('type')
        next_url = request.args.get('next', url_for('login')) # Default redirect to login

        if token_hash and auth_type:
            app_logger.info(f"Auth confirmation request received. Type: {auth_type}, Token Hash: {token_hash[:5]}..., Next: {next_url}")

            try:
                # Access the Supabase client
                from app import supabase

                if not supabase:
                    error_logger.critical("Supabase client not available in confirm_auth route.")
                    flash('Authentication system configuration error.', 'error')
                    return redirect(url_for('login'))

                app_logger.info(f"Attempting Supabase verify_otp with type: {auth_type}, token_hash: {token_hash[:5]}...")
                # Verify the OTP (which includes password reset tokens)
                # Get the email from the query parameters if available
                email = request.args.get('email')

                # Use the correct schema for verifying OTP
                verify_data = {
                    "token_hash": token_hash,
                    "type": auth_type
                }

                # Add email to the verification data if available
                if email:
                    verify_data["email"] = email
                    app_logger.info(f"Including email in OTP verification: {email}")
                else:
                    app_logger.info(f"Email not found in query parameters, proceeding without it")

                response = supabase.auth.verify_otp(verify_data)
                app_logger.info(f"Supabase verify_otp response: {response}")

                # Check if verification was successful (user data should be present)
                if response and response.user:
                    supabase_email = response.user.email
                    app_logger.info(f"Supabase user verified: {supabase_email}")

                    # Validate the next_url to prevent open redirect vulnerabilities
                    # Allow only relative paths starting with '/'
                    if next_url and next_url.startswith('/'):
                        # Store the Supabase email and token_hash in the session for use in the reset_password route
                        session['supabase_email'] = supabase_email
                        session['token_hash'] = token_hash
                        session['auth_timestamp'] = datetime.now().timestamp()  # Add timestamp for session freshness check
                        session.permanent = True  # Make session persistent
                        session.modified = True  # Mark session as modified to ensure it's saved

                        # Add the email and token to the redirect URL as query parameters as a fallback
                        # URL encode the parameters to ensure they're properly formatted
                        encoded_email = urllib.parse.quote(supabase_email)
                        encoded_token = urllib.parse.quote(token_hash)
                        redirect_url = f"{next_url}?email={encoded_email}&token_hash={encoded_token}"
                        app_logger.info(f"Adding email and token to redirect URL as fallback: {redirect_url}")

                        # Flash success message
                        flash('Verification successful! Please set your new password.', 'success')
                        return redirect(redirect_url) # Redirect to the URL with query parameters as fallback
                    else:
                        # Invalid 'next' URL provided
                        error_logger.warning(f"Invalid 'next' URL provided in confirmation link: {next_url}. Redirecting verified user {supabase_email} to login.")
                        flash('Verification link error. Redirecting to login.', 'warning')
                        return redirect(url_for('login'))
                else:
                    error_logger.error(f"Supabase verify_otp failed for type {auth_type}. Response: {response}")
                    flash('Verification failed. The link may be invalid or expired.', 'error')
                    return redirect(url_for('login'))

            except Exception as e:
                # Catch potential exceptions from Supabase client or other issues
                error_logger.exception(f"Error during auth confirmation ({auth_type}): {str(e)}")
                # Check for specific Supabase errors if possible (e.g., invalid token)
                if "invalid token" in str(e).lower():
                    flash('Verification failed: Invalid or expired link.', 'error')
                else:
                    flash('An unexpected error occurred during verification.', 'error')
                return redirect(url_for('login'))

        # If we get here, neither code nor token_hash+type were provided
        error_logger.warning("Auth confirmation missing required parameters (code or token_hash+type).")
        flash('Invalid or incomplete confirmation link.', 'error')
        return redirect(url_for('login'))
    # --- END: New Supabase Auth Confirmation Route ---


    # --- Other API Endpoints ---

    @app.route("/check_answer/<int:question_id>/<int:part_id>", methods=['POST'])
    @limiter.limit("30/minute") # Apply rate limit
    @login_required # Require login to check answers
    def check_answer(question_id, part_id):
        if 'user_id' not in session:
            error_logger.warning("Unauthorized attempt to submit answer")
            return jsonify({
                'status': 'error',
                'message': 'Please login to submit answers'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)

        # Handle both text and image inputs
        user_answer = request.form.get('answer', '').strip()
        image_file = request.files.get('image')
        confidence_level = request.form.get('confidence_level', 'Medium')

        if not user_answer and not image_file:
            error_logger.info(f"Empty answer submitted - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")
            return jsonify({
                'status': 'error',
                'message': 'Please provide an answer or upload an image'
            }), 400

        try:
            # Process image if provided
            if image_file:
                # Validate file type
                if not image_file.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid file type. Please upload a PNG, JPG, or JPEG image.'
                    }), 400

                # Encode the image to base64
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Initialize Gemini vision model
                gemini_vision_model = genai.GenerativeModel('gemini-2.5-pro-preview-05-06')

                # Create multimodal prompt
                prompt_parts = [
                    "Convert this mathematical image to LaTeX format. Return ONLY the LaTeX code without any additional text, comments, or formatting. Include ALL mathematical content from the image.",
                    {"mime_type": "image/jpeg", "data": base64_image}
                ]

                # Generate content
                response = gemini_vision_model.generate_content(prompt_parts)
                user_answer = response.text
                app_logger.info(f"Image received for OCR - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")

            # Placeholder for score and feedback as 'grader' module is removed.
            # Grading is now handled by get_git_diff route.
            # This route will now primarily log the submission attempt.
            current_score = 0
            feedback_to_user = "Your answer has been submitted. Detailed feedback will be available via the standard grading process."

            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer,
                score=current_score
            )
            db.session.add(submission)
            db.session.commit()

            app_logger.info(
                f"Answer submitted - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, "
                f"Score: {submission.score}/{part_data.score}"
            )

            return jsonify({
                'status': 'success',
                'feedback': feedback_to_user,
                'score': submission.score,
                'submission_id': submission.id,
                'max_score': part_data.score
            })

        except Exception as e:
            db.session.rollback()
            error_logger.exception(
                f"Error processing submission - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while processing your answer'
            }), 500



    @app.route('/get_git_diff/<int:question_id>/<int:part_id>', methods=['POST'])
    @login_required
    @limiter.limit("30/minute")
    def get_git_diff(question_id, part_id):
        # Start overall timing
        overall_start_time = time.time()
        step_timings = {}

        # Initialize Gemini model
        init_start_time = time.time()
        gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
        pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
        index = pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))
        init_end_time = time.time()
        step_timings['initialization'] = round((init_end_time - init_start_time) * 1000, 2)

        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to submit answers'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)

        # Handle both text and image submissions
        input_start_time = time.time()
        user_answer = request.form.get('answer', '').strip()
        image_file = request.files.get('image')

        # If an image was submitted, process it
        if image_file and image_file.filename:
            try:
                ocr_start_time = time.time()
                # Convert image to base64 for API processing
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Initialize Gemini vision model
                gemini_vision_model = genai.GenerativeModel('gemini-2.5-pro-preview-05-06')

                # Create multimodal prompt
                prompt_parts = [
                    "You are a LaTeX expert. Convert this mathematical image to LaTeX format. Return ONLY the LaTeX code without any additional text, comments, or formatting. Include ALL mathematical content from the image.",
                    {"mime_type": "image/jpeg", "data": base64_image}
                ]

                # Generate content
                response = gemini_vision_model.generate_content(prompt_parts)
                user_answer = response.text
                ocr_end_time = time.time()
                step_timings['ocr_processing'] = round((ocr_end_time - ocr_start_time) * 1000, 2)
            except Exception as e:
                error_logger.exception(f"Error processing image submission: {str(e)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Error processing image submission'
                }), 500

        input_end_time = time.time()
        step_timings['input_processing'] = round((input_end_time - input_start_time) * 1000, 2)

        if not user_answer:
            return jsonify({
                'status': 'error',
                'message': 'Please provide an answer'
            }), 400

        # Use the new helper function to calculate score and get evaluated points
        # Ensure gemini_model is initialized (it is at the start of get_git_diff)
        grading_start_time = time.time()
        grading_details = _calculate_score_and_evaluated_points(user_answer, part_data, gemini_model, app_logger, index)
        grading_end_time = time.time()
        step_timings['grading'] = round((grading_end_time - grading_start_time) * 1000, 2)

        total_score = grading_details['score']
        evaluated_points = grading_details['evaluated_points']
        grading_timing_info = grading_details.get('timing_info', {})

        if 'error' in grading_details and part_data.input_type == 'mcq': # Handle MCQ specific errors from helper
            return jsonify({'status': 'error', 'message': grading_details['error']}), 400

        # If it's an MCQ, the helper already determined the score and basic feedback.
        # We can return a simplified response for MCQs directly after calling the helper.
        if part_data.input_type == 'mcq':
            # Create submission record
            db_start_time = time.time()
            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer,  # user_answer is the selected_option_index for MCQ
                score=total_score
            )
            db.session.add(submission)
            db.session.commit()
            db_end_time = time.time()
            step_timings['database_operations'] = round((db_end_time - db_start_time) * 1000, 2)

            # Calculate total time
            overall_end_time = time.time()
            step_timings['total_time'] = round((overall_end_time - overall_start_time) * 1000, 2)

            is_correct_mcq = total_score > 0
            return jsonify({
                'status': 'success',
                'score': total_score,
                'max_score': part_data.score,
                'is_correct': is_correct_mcq,
                'feedback': 'Your answer is correct.' if is_correct_mcq else 'Your answer is incorrect.',
                'marking_points': evaluated_points, # Contains the single MCQ point
                'timing_info': {
                    'step_timings': step_timings,
                    'grading_details': grading_timing_info
                }
            })

        # For non-MCQ, proceed with highlighting and detailed response
        try:
            # Prepare data for highlighting with feedback information
            highlighting_start_time = time.time()
            import html # Ensure html module is imported
            highlight_data = []
            for point in evaluated_points:
                if (point.get('achieved') or point.get('partial')) and point.get('evidence') and point.get('color'):
                    # Use a dashed border for partial answers, solid for full credit
                    border_style = "border-b-2 " if point.get('achieved') else "border-b-2 border-dashed "
                    highlight_data.append({
                        'evidence': point['evidence'],
                        'color_class': border_style + point['color'], # e.g., 'border-yellow-400'
                        'feedback': point.get('feedback', ''), # Include structured feedback
                        'description': point.get('description', ''), # Include marking point description
                        'achieved': point.get('achieved', False),
                        'partial': point.get('partial', False),
                        'score': point.get('achieved_score', 0)
                    })

            highlighted_answer = html.escape(user_answer) # Default to escaped original answer
            if highlight_data:
                # Create the prompt for highlighting with feedback information
                highlight_prompt = r"""\
                You are an HTML expert. Given a piece of text and a list of evidence snippets with associated Tailwind CSS border classes and feedback, modify the original text by wrapping EACH evidence snippet EXACTLY as it appears in the text with a span tag that includes both highlighting and tooltip feedback.

                **Instructions:**
                1. Find the occurrences of the evidence snippets within the original text. Do NOT modify the user answer (including changing the order of the answer), just highlight the relevant parts.
                2. Wrap each found evidence snippet with a span tag that includes:
                   - The color class for visual highlighting
                   - A title attribute containing the structured feedback for tooltips
                   - Format: <span class="{{color_class}}" title="{{feedback}}">evidence text</span>
                3. If points overlap, you need to deconflict them in the way that makes the most sense.
                4. Output ONLY the final, complete HTML string. Do not include any introductory text, explanations, or markdown formatting.
                5. Ensure ALL highlighted data is included and inside the appropriately colored spans with feedback tooltips.
                6. Do NOT write stuff like Note: xxx at the end of your output if there are ambiguities.
                7. The order of the marking points in the user answer may be jumbled up. However, you should still give credit and underline parts even if they aren't in order.
                8. Make sure the title attribute is properly escaped for HTML (replace quotes with &quot; if needed).
                9. Write your final result as: FINAL RESULT: <your_html_output>
                """
                other=f"""**User answer:**
                {user_answer}

                **Highlight Data (Evidence, CSS Class, and Feedback):**
                {json.dumps(highlight_data, indent=2)}

                **Example format for each highlighted section:**
                <span class="border-b-2 border-yellow-400" title="Correctly identified kinetic energy formula">KE = 1/2mv²</span>

                **Final HTML Output:**
                """
                highlight_prompt = highlight_prompt + other
                try:
                    # Use Gemini 2.5 Pro for highlighting with strict parameters
                    highlight_llm_start_time = time.time()
                    gemini_pro_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')

                    generation_config = {
                        "temperature": 0.3,  # Use deterministic output
                        "top_p": 0.95,
                        "top_k": 40,
                        "max_output_tokens": 2048,
                    }

                    safety_settings = [
                        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                    ]

                    highlighted_answer_raw = gemini_pro_model.generate_content(
                        highlight_prompt,
                        generation_config=generation_config,
                        safety_settings=safety_settings
                    ).text.strip()

                    highlight_llm_end_time = time.time()
                    step_timings['highlighting_llm'] = round((highlight_llm_end_time - highlight_llm_start_time) * 1000, 2)

                    print(highlighted_answer_raw)
                    highlighted_answer_raw = highlighted_answer_raw.split('FINAL RESULT: ')[1]



                    # Basic validation (optional but recommended)
                    if '<span' in highlighted_answer_raw and '</span>' in highlighted_answer_raw:
                        highlighted_answer = highlighted_answer_raw
                    else:
                        # Log a warning if the response doesn't look like HTML
                        error_logger.warning(f"Gemini highlighting response did not seem to contain valid spans: {highlighted_answer_raw}")
                        # Fallback to the default escaped answer
                        highlighted_answer = html.escape(user_answer)

                except Exception as e:
                    error_logger.exception(f"Error calling Gemini API for highlighting: {str(e)}")
                    # Fallback to the default escaped answer in case of API error
                    highlighted_answer = html.escape(user_answer)
                    step_timings['highlighting_llm'] = 0  # Set to 0 if there was an error

            highlighting_end_time = time.time()
            step_timings['highlighting'] = round((highlighting_end_time - highlighting_start_time) * 1000, 2)
            # --- END NEW CODE ---

            # Create a new submission record in the database
            db_start_time = time.time()
            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer, # Store original answer
                score=total_score
            )

            # Add and commit to the database
            db.session.add(submission)
            db.session.commit()
            db_end_time = time.time()
            step_timings['database_operations'] = round((db_end_time - db_start_time) * 1000, 2)

            # Calculate total time
            overall_end_time = time.time()
            step_timings['total_time'] = round((overall_end_time - overall_start_time) * 1000, 2)

            # Log the highlighting data and result for debugging
            app_logger.info(f"Highlighting data: {json.dumps(highlight_data)}")
            app_logger.info(f"Highlighted answer: {highlighted_answer}")
            app_logger.info(f"Timing information: {json.dumps(step_timings)}")

            return jsonify({
                'status': 'success',
                'marking_points': evaluated_points,
                'score': total_score,
                'max_score': part_data.score,
                # --- START MODIFIED CODE ---
                'answer': highlighted_answer, # Return highlighted answer
                'timing_info': {
                    'step_timings': step_timings,
                    'grading_details': grading_timing_info
                }
                # --- END MODIFIED CODE ---
            })

        except Exception as e:
            error_logger.exception(
                f"Error processing answer - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while processing your answer'
            }), 500


    @app.route('/auto_save', methods=['POST'])
    @login_required
    def auto_save():
        """Saves incomplete progress for a problem set part."""
        user_id = session['user_id']
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request format.'}), 400

        problemset_id = data.get('problemset_id')
        question_id = data.get('question_id')
        part_id = data.get('part_id')
        answer = data.get('answer') # Can be None or empty

        if not all([problemset_id, question_id, part_id]):
            return jsonify({'status': 'error', 'message': 'Missing required data (problemset, question, part IDs).'}), 400

        try:
            # Get or create incomplete submission record with retry logic built into the model method
            submission = IncompleteSubmission.get_or_create(
                problemset_id=problemset_id,
                user_id=user_id,
                question_id=question_id,
                part_id=part_id
            )

            # Update answer and timestamp
            from sqlalchemy.exc import OperationalError
            import time

            max_retries = 3
            retry_delay = 0.1  # seconds

            for attempt in range(max_retries):
                try:
                    submission.answer = answer # Allow None or empty string
                    submission.last_updated = datetime.now()
                    db.session.commit()
                    # app_logger.debug(f"Autosaved progress for User: {user_id}, PS: {problemset_id}, Q: {question_id}, P: {part_id}")
                    return jsonify({'status': 'success'})

                except OperationalError as e:
                    # Handle database locks specifically
                    if "database is locked" in str(e) and attempt < max_retries - 1:
                        db.session.rollback()
                        time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                        continue
                    else:
                        db.session.rollback()
                        raise

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error during auto_save for User {user_id}, PS {problemset_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Failed to save progress.'}), 500


    @app.route('/get_saved_progress/<int:problemset_id>')
    @login_required
    def get_saved_progress(problemset_id):
        """Retrieves saved incomplete progress for a problem set."""
        user_id = session['user_id']
        try:
            submissions = IncompleteSubmission.query.filter_by(
                problemset_id=problemset_id,
                user_id=user_id
            ).all()

            # Format data for the frontend
            progress_data = [{
                'question_id': sub.question_id,
                'part_id': sub.part_id,
                'answer': sub.answer if sub.answer is not None else '' # Ensure frontend gets string
            } for sub in submissions]

            return jsonify({
                'status': 'success',
                'submissions': progress_data
            })
        except Exception as e:
            error_logger.exception(f"Error fetching saved progress for User {user_id}, PS {problemset_id}: {e}")
            # Return 200 OK but with error status for AJAX handling
            return jsonify({'status': 'error', 'message': 'Could not retrieve saved progress.'}), 200


    @app.route('/submit_problemset', methods=['POST'])
    @limiter.limit("10/minute") # Limit submission frequency
    @login_required
    def submit_problemset():
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        data = request.get_json()
        problemset_id = data.get('problemset_id')
        submissions = data.get('submissions', [])  # List of {question_id, part_id, answer}

        if not problemset_id:
            return jsonify({'status': 'error', 'message': 'Missing problemset_id'}), 400

        # Initialize the Gemini model once for all parts in this problemset submission
        gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')

        try:
            # Create a new problemset submission
            problemset_submission = ProblemSetSubmission(
                problemset_id=problemset_id,
                user_id=session['user_id'],
                status='completed',
                submitted_at=datetime.now()
            )
            db.session.add(problemset_submission)
            db.session.flush()  # Get the ID of the new submission

            # Process each answer
            submission_results = []
            for submission_data in submissions:
                question_id = submission_data.get('question_id')
                part_id = submission_data.get('part_id')
                answer = submission_data.get('answer', '').strip()

                if not all([question_id, part_id]):
                    continue

                # Get the part to check its answer and score
                part = Part.query.get(part_id)
                if not part:
                    continue

                # Use the helper function to calculate the score for this part
                # Note: submit_problemset doesn't have Pinecone index, so pass None
                grading_details = _calculate_score_and_evaluated_points(answer, part, gemini_model, app_logger, None)
                current_score = grading_details['score']
                # We might not need detailed evaluated_points here unless we plan to store them
                # For now, feedback_message can be generic or based on score.
                feedback_message = "Answer submitted and graded." # More accurate message

                # Create the submission
                submission = Submission(
                    user_id=session['user_id'],
                    question_id=question_id,
                    part_id=part_id,
                    answer=answer,
                    score=current_score
                )
                db.session.add(submission)
                problemset_submission.question_submissions.append(submission)

                # Add submission result with feedback
                submission_results.append({
                    'question_id': question_id,
                    'part_id': part_id,
                    'score': current_score,
                    'max_score': part.score,
                    'feedback': feedback_message
                })

            # Calculate scores
            problemset_submission.calculate_scores()

            # Delete incomplete submissions for this problemset
            IncompleteSubmission.query.filter_by(
                problemset_id=problemset_id,
                user_id=session['user_id']
            ).delete()

            db.session.commit()
            return jsonify({
                'status': 'success',
                'message': 'Problem set submitted successfully',
                'submission_id': problemset_submission.id,
                'submissions': submission_results
            })

        except Exception as e:
            db.session.rollback()
            print(f"Error submitting problem set: {str(e)}")
            return jsonify({'status': 'error', 'message': str(e)}), 500



    # --- Marking Point AJAX Endpoints ---

    @app.route('/extract_marking_points/<int:part_id>', methods=['POST'])
    @login_required # Should be admin_required?
    def extract_marking_points(part_id):
        """Extract marking points from a question part using an API"""
        part = Part.query.get_or_404(part_id)

        try:
            # Here you would call your API to extract marking points
            # This is a placeholder for the API call
            # api_response = your_api_call(part.description, part.answer)

            # For now, we'll create a sample response
            api_response = {
                'marking_points': [
                    {'description': 'Correct formula', 'score': 2.0},
                    {'description': 'Correct substitution', 'score': 1.0},
                    {'description': 'Correct final answer', 'score': 1.0}
                ]
            }

            # Clear existing auto-generated marking points
            MarkingPoint.query.filter_by(
                part_id=part_id,
                is_auto_generated=True
            ).delete()

            # Add new marking points
            for i, point in enumerate(api_response['marking_points']):
                marking_point = MarkingPoint(
                    part_id=part_id,
                    description=point['description'],
                    score=point['score'],
                    order=i,
                    is_auto_generated=True
                )
                db.session.add(marking_point)

            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': 'Marking points extracted successfully',
                'marking_points': [{
                    'id': mp.id,
                    'description': mp.description,
                    'score': mp.score,
                    'order': mp.order
                } for mp in part.marking_points]
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500


    @app.route('/update_marking_point/<int:marking_point_id>', methods=['PUT'])
    @login_required # Should be admin_required?
    def update_marking_point(marking_point_id):
        """Updates a specific marking point."""
        # Add admin check if necessary

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request format.'}), 400

        try:
            updated = False
            if 'description' in data:
                new_desc = data['description'].strip()
                if new_desc: # Don't allow empty description
                     marking_point.description = new_desc
                     updated = True
                else:
                     return jsonify({'status': 'error', 'message': 'Description cannot be empty.'}), 400
            if 'score' in data:
                try:
                    new_score = float(data['score'])
                    if new_score < 0: # Allow 0 score? Let's prevent negative for now.
                         return jsonify({'status': 'error', 'message': 'Score cannot be negative.'}), 400
                    marking_point.score = new_score
                    updated = True
                except (ValueError, TypeError):
                     return jsonify({'status': 'error', 'message': 'Invalid score format.'}), 400
            if 'order' in data:
                 try:
                     marking_point.order = int(data['order'])
                     updated = True
                 except (ValueError, TypeError):
                      return jsonify({'status': 'error', 'message': 'Invalid order format.'}), 400

            if updated:
                # marking_point.validate() # Call validation if defined in model
                marking_point.is_auto_generated = False # Manual edit overrides auto-gen flag
                db.session.commit()
                app_logger.info(f"Updated marking point {marking_point_id}")
                return jsonify({
                    'status': 'success',
                    'message': 'Marking point updated.',
                    'marking_point': { # Return updated data
                        'id': marking_point.id,
                        'description': marking_point.description,
                        'score': marking_point.score,
                        'order': marking_point.order,
                        'is_auto_generated': marking_point.is_auto_generated
                    }
                })
            else:
                 return jsonify({'status': 'info', 'message': 'No changes detected.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error updating marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error updating marking point.'}), 500


    @app.route('/delete_marking_point/<int:marking_point_id>', methods=['DELETE'])
    @login_required # Should be admin_required?
    def delete_marking_point(marking_point_id):
        """Deletes a specific marking point."""
        # Add admin check if necessary

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        part_id = marking_point.part_id # Get part ID for logging

        try:
            db.session.delete(marking_point)
            db.session.commit()
            app_logger.info(f"Deleted marking point {marking_point_id} from Part {part_id}")
            return jsonify({'status': 'success', 'message': 'Marking point deleted.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error deleting marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error deleting marking point.'}), 500


    @app.route('/add_marking_point/<int:part_id>', methods=['POST'])
    @login_required # Should be admin_required?
    def add_marking_point(part_id):
        try:
            data = request.get_json()
            part = Part.query.get_or_404(part_id)

            # Create new marking point
            marking_point = MarkingPoint(
                part_id=part_id,
                description=data.get('description', 'New marking point'),
                score=float(data.get('score', 1.0)),
                order=int(data.get('order', 0)),
                is_auto_generated=False
            )

            # Validate the marking point
            marking_point.validate()

            # Add to database
            db.session.add(marking_point)
            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': 'Marking point added successfully',
                'marking_point': {
                    'id': marking_point.id,
                    'description': marking_point.description,
                    'score': marking_point.score,
                    'order': marking_point.order
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 400



    @app.route('/move_marking_point/<int:marking_point_id>/<direction>', methods=['POST'])
    @login_required # Should be admin_required?
    def move_marking_point(marking_point_id, direction):
        """Moves a marking point up or down in order within its part."""
        # Add admin check if necessary

        if direction not in ['up', 'down']:
            return jsonify({'status': 'error', 'message': 'Invalid direction.'}), 400

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        part_id = marking_point.part_id

        try:
            # Get all marking points for this part, ordered correctly
            siblings = MarkingPoint.query.filter_by(part_id=part_id)\
                                      .order_by(MarkingPoint.order).all()

            try:
                current_index = siblings.index(marking_point)
            except ValueError:
                 # Should not happen if MP exists and belongs to the part
                 error_logger.error(f"Marking point {marking_point_id} not found in siblings list for Part {part_id}")
                 return jsonify({'status': 'error', 'message': 'Marking point consistency error.'}), 500

            if direction == 'up' and current_index > 0:
                # Swap order with the previous sibling
                prev_sibling = siblings[current_index - 1]
                marking_point.order, prev_sibling.order = prev_sibling.order, marking_point.order
                db.session.commit()
                app_logger.info(f"Moved marking point {marking_point_id} up")
                return jsonify({'status': 'success', 'message': 'Moved up.'})
            elif direction == 'down' and current_index < len(siblings) - 1:
                # Swap order with the next sibling
                next_sibling = siblings[current_index + 1]
                marking_point.order, next_sibling.order = next_sibling.order, marking_point.order
                db.session.commit()
                app_logger.info(f"Moved marking point {marking_point_id} down")
                return jsonify({'status': 'success', 'message': 'Moved down.'})
            else:
                # Cannot move further in this direction
                return jsonify({'status': 'info', 'message': 'Cannot move further.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error moving marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error moving marking point.'}), 500

    # --- Other API Endpoints ---

    @app.route("/explain_answer/<int:question_id>/<int:part_id>")
    @login_required
    @limiter.limit("10/minute")
    def explain_answer(question_id, part_id):
        """
        Explains the answer for a specific question part using Gemini LLM.
        Returns a streaming response with the explanation.
        """
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to get answer explanations'
            }), 401

        update_user_activity(session['user_id'])

        try:
            # Get the part data
            part_data = Part.query.get_or_404(part_id)
            question_data = Question.query.get_or_404(question_id)

            # Initialize Gemini model
            gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')

            # Construct the prompt
            if part_data.input_type == 'mcq':
                # For MCQ, include the options and correct answer
                options_text = ""
                correct_option = None

                for i, option in enumerate(part_data.options):
                    options_text += f"Option {i+1}: {option.description}\n"
                    if option.is_correct:
                        correct_option = i+1

                prompt = f"""
                You are an expert educational assistant. Explain the following multiple-choice question and why the correct answer is the best choice.

                QUESTION: {part_data.description}

                OPTIONS:
                {options_text}

                CORRECT ANSWER: Option {correct_option}

                Provide a VERY CONCISE explanation (maximum 150 words) of why this is the correct answer. Focus on the key concepts and principles.

                Format your response using this structure:
                # Key Concept
                Brief explanation of the main concept being tested

                # Why Option {correct_option} is Correct
                Concise explanation with clear reasoning

                Use LaTeX for mathematical expressions: $...$ for inline and $$...$$ for display math.
                """
            else:
                # For text/SAQ questions
                prompt = f"""
                You are an expert educational assistant. Explain the following question and its answer.

                QUESTION: {part_data.description}

                MODEL ANSWER: {part_data.answer}

                Provide a VERY CONCISE explanation (maximum 150 words) focusing only on the key concepts and reasoning.

                Format your response using this structure:
                # Key Concept
                Brief explanation of the main concept being tested (1-2 sentences)

                # Solution Approach
                Concise explanation of the approach to solve this problem (2-3 sentences)

                # Critical Points
                * Point 1
                * Point 2

                Use LaTeX for mathematical expressions: $...$ for inline and $$...$$ for display math.
                """

            # Create a streaming response
            def generate():
                generation_config = {
                    "temperature": 0.3,  # Slightly creative but mostly factual
                    "top_p": 0.95,
                    "top_k": 40,
                    "max_output_tokens": 4096,
                }

                safety_settings = [
                    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                ]

                response = gemini_model.generate_content(
                    prompt,
                    generation_config=generation_config,
                    safety_settings=safety_settings,
                    stream=True
                )

                for chunk in response:
                    if chunk.text:
                        yield chunk.text

            app_logger.info(f"Answer explanation requested - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")
            return app.response_class(generate(), mimetype='text/plain')

        except Exception as e:
            error_logger.exception(f"Error generating answer explanation: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while generating the explanation'
            }), 500

    @app.route("/get_activity_data/<int:user_id>")
    @login_required # Or admin_required? Check permissions needed.
    def get_activity_data(user_id):
        """Retrieves daily activity counts for a user."""
        # Security check: Ensure the requesting user has permission to view this data
        # e.g., is the user themselves or an admin.
        requesting_user_id = session['user_id']
        requesting_user = User.query.get(requesting_user_id)
        if user_id != requesting_user_id and (not requesting_user or requesting_user.role != 'admin'):
             return jsonify({'status': 'error', 'message': 'Permission denied.'}), 403

        try:
            # Query activities for the specified user
            activities = DailyActivity.query.filter_by(user_id=user_id).all()
            # Format data as date string -> count dictionary
            activity_data = {activity.date.isoformat(): activity.activity_count for activity in activities}

            return jsonify(activity_data)
        except Exception as e:
             error_logger.exception(f"Error fetching activity data for user {user_id}: {e}")
             return jsonify({'status': 'error', 'message': 'Could not retrieve activity data.'}), 500

    @app.route("/get_daily_time_data/<int:user_id>")
    @login_required
    def get_daily_time_data(user_id):
        """Retrieves daily active time data for a user."""
        # Security check: Ensure the requesting user has permission to view this data
        requesting_user_id = session['user_id']
        requesting_user = User.query.get(requesting_user_id)
        if user_id != requesting_user_id and (not requesting_user or requesting_user.role != 'admin'):
             return jsonify({'status': 'error', 'message': 'Permission denied.'}), 403

        try:
            # Query daily active time for the specified user
            daily_times = DailyActiveTime.query.filter_by(user_id=user_id).all()
            # Format data as date string -> seconds dictionary
            time_data = {time_entry.date.isoformat(): time_entry.active_time for time_entry in daily_times}

            return jsonify(time_data)
        except Exception as e:
             error_logger.exception(f"Error fetching daily time data for user {user_id}: {e}")
             return jsonify({'status': 'error', 'message': 'Could not retrieve daily time data.'}), 500

    @app.route("/update_active_time", methods=['POST'])
    @login_required
    def update_active_time():
        """Updates the active time for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']
        data = request.get_json()

        if not data or 'seconds' not in data:
            return jsonify({'status': 'error', 'message': 'Missing seconds parameter'}), 400

        try:
            seconds = int(data['seconds'])
            if seconds <= 0:
                return jsonify({'status': 'error', 'message': 'Seconds must be positive'}), 400

            # Get today's date
            today = datetime.now().date()

            # Get or create the DailyActiveTime record for today
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            if not daily_active_time:
                daily_active_time = DailyActiveTime(
                    user_id=user_id,
                    date=today,
                    active_time=0
                )
                db.session.add(daily_active_time)

            # Update the active time
            daily_active_time.active_time += seconds

            # Update the user's last_active timestamp
            user = User.query.get(user_id)
            if user:
                user.last_active = datetime.now()

            db.session.commit()

            # Return the updated active time
            return jsonify({
                'status': 'success',
                'active_time': daily_active_time.active_time
            })

        except ValueError:
            return jsonify({'status': 'error', 'message': 'Invalid seconds value'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error updating active time for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not update active time'}), 500

    @app.route("/get_active_time", methods=['GET'])
    @login_required
    def get_active_time():
        """Gets the total active time for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']

        try:
            # Get today's date
            today = datetime.now().date()

            # Get the DailyActiveTime record for today
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            today_time = daily_active_time.active_time if daily_active_time else 0

            # Get total active time across all days
            from sqlalchemy import func
            total_time_result = db.session.query(func.sum(DailyActiveTime.active_time)).filter(
                DailyActiveTime.user_id == user_id
            ).first()

            total_time = total_time_result[0] if total_time_result[0] else 0

            # Get the user's daily time goal
            user = User.query.get(user_id)
            daily_goal = user.daily_time_goal if user and user.daily_time_goal else 3600  # Default: 1 hour

            # Calculate progress percentage
            progress_percent = min(round((today_time / daily_goal) * 100), 100) if daily_goal > 0 else 0

            return jsonify({
                'status': 'success',
                'today_time': today_time,
                'total_time': total_time,
                'daily_goal': daily_goal,
                'progress_percent': progress_percent
            })

        except Exception as e:
            error_logger.exception(f"Error getting active time for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not retrieve active time'}), 500

    @app.route("/set_time_goal", methods=['POST'])
    @login_required
    def set_time_goal():
        """Sets the daily time goal for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']
        data = request.get_json()

        if not data or 'goal_minutes' not in data:
            return jsonify({'status': 'error', 'message': 'Missing goal_minutes parameter'}), 400

        try:
            goal_minutes = int(data['goal_minutes'])
            if goal_minutes <= 0:
                return jsonify({'status': 'error', 'message': 'Goal minutes must be positive'}), 400

            # Convert minutes to seconds
            goal_seconds = goal_minutes * 60

            # Update the user's daily time goal
            user = User.query.get(user_id)
            if not user:
                return jsonify({'status': 'error', 'message': 'User not found'}), 404

            user.daily_time_goal = goal_seconds
            db.session.commit()

            # Get today's active time for progress calculation
            today = datetime.now().date()
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            today_time = daily_active_time.active_time if daily_active_time else 0

            # Calculate progress percentage
            progress_percent = min(round((today_time / goal_seconds) * 100), 100) if goal_seconds > 0 else 0

            return jsonify({
                'status': 'success',
                'daily_goal': goal_seconds,
                'goal_minutes': goal_minutes,
                'progress_percent': progress_percent
            })

        except ValueError:
            return jsonify({'status': 'error', 'message': 'Invalid goal_minutes value'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error setting time goal for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not set time goal'}), 500
